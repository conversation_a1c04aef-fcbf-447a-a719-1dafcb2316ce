from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>

from .views import *
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)

from .views import CustomTokenObtainPairView


# Создаем router для игр
router = DefaultRouter()
router.register(r'games', GameViewSet, basename='games')
router.register(r'cart', CartItemViewSet, basename='cart')
router.register(r'game-keys', GameKeyViewSet, basename='game-keys')


urlpatterns = [

    # register user
    path('api/auth/register/', RegisterView.as_view(), name='register'),
    path('api/auth/verify-code/', VerifyCodeView.as_view(), name='verify-code'),
    path('api/auth/resend-code/', ResendCodeView.as_view(), name='resend-code'),
    path('api/userprofile/', UserProfile.as_view(), name='me'),
    path('api/users/', UserList.as_view(), name='user-list'),




    # tokens
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/token/verify/', TokenVerifyView.as_view(), name='token_verify'),


    # game library
    path('api/library/', UserLibraryListView.as_view(), name='user-library'),
    path('api/library/add/', AddToLibraryView.as_view(), name='add-to-library'),


    path('api/', include(router.urls)),

]