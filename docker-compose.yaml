services:
  web:
    build: .
    container_name: django_app
    command: gunicorn --bind 0.0.0.0:8000 --workers 3 DjangoBaseApp.wsgi:application
    volumes:
      - .:/app
    expose:
      - "8000"  # Теперь не публикуем, а только открываем для Nginx
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - minio
    networks:
      - dev

  minio:
    image: quay.io/minio/minio
    container_name: minio
    ports:
      - "9000:9000"  # API порт MinIO
      - "9001:9001"  # Порт консоли MinIO (веб-интерфейс)
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data  # Используем именованный том для постоянного хранения
    command: server /data --console-address ":9001"
    restart: always # Автоматический перезапуск контейнера при сбое
    networks:
      - dev

  nginx:
    image: nginx:stable-alpine
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./staticfiles:/static
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - web
    networks:
      - dev

volumes:
  sqlite_data:
  minio_data:

networks:
  dev: