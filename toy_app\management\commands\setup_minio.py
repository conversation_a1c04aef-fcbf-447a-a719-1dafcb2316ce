from django.core.management.base import BaseCommand
from django.conf import settings
from minio import Minio
from minio.error import S3Error


class Command(BaseCommand):
    help = 'Setup MinIO bucket and test connection'

    def handle(self, *args, **options):
        self.stdout.write("Setting up MinIO...")
        
        try:
            # Get MinIO settings
            endpoint = settings.MINIO_ENDPOINT.replace('http://', '').replace('https://', '')
            access_key = settings.MINIO_ACCESS_KEY
            secret_key = settings.MINIO_SECRET_KEY
            bucket_name = settings.MINIO_BUCKET_NAME
            
            self.stdout.write(f"Connecting to Min<PERSON> at: {endpoint}")
            self.stdout.write(f"Using bucket: {bucket_name}")
            
            # Create MinIO client
            client = Minio(
                endpoint,
                access_key=access_key,
                secret_key=secret_key,
                secure=False  # Set to True if using HTTPS
            )
            
            # Check if bucket exists
            if not client.bucket_exists(bucket_name):
                self.stdout.write(f"Creating bucket '{bucket_name}'...")
                client.make_bucket(bucket_name)
                self.stdout.write(
                    self.style.SUCCESS(f"Bucket '{bucket_name}' created successfully!")
                )
            else:
                self.stdout.write(f"Bucket '{bucket_name}' already exists.")
            
            # Test connection with a simple upload/download
            test_content = b"MinIO connection test"
            test_file = "connection-test.txt"
            
            client.put_object(
                bucket_name,
                test_file,
                data=test_content,
                length=len(test_content),
                content_type="text/plain"
            )
            
            # Download and verify
            response = client.get_object(bucket_name, test_file)
            downloaded_content = response.read()
            
            # Clean up
            client.remove_object(bucket_name, test_file)
            
            if downloaded_content == test_content:
                self.stdout.write(
                    self.style.SUCCESS("✅ MinIO setup and connection test successful!")
                )
            else:
                self.stdout.write(
                    self.style.ERROR("❌ Connection test failed - content mismatch")
                )
                
        except S3Error as e:
            self.stdout.write(
                self.style.ERROR(f"❌ MinIO S3 Error: {e}")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error: {e}")
            )
