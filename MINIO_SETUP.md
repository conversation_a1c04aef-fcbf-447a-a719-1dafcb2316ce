# MinIO Setup for Django

This guide explains how to set up MinIO for file storage in your Django application.

## What was changed

### 1. Environment Variables (.env)
Updated MinIO credentials and endpoint:
```
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_BUCKET_NAME=toytoi-media
MINIO_ENDPOINT=http://minio:9000
```

### 2. Django Settings (DjangoBaseApp/settings.py)
- Replaced deprecated `DEFAULT_FILE_STORAGE` with modern `STORAGES` configuration
- Added proper MinIO/S3 settings
- Kept `MEDIA_URL` and `MEDIA_ROOT` for compatibility

### 3. Docker Compose (docker-compose.yaml)
- Added MinIO service
- Removed local media volume mounts (files now stored in MinIO)
- Added dependency from web to minio service

### 4. Nginx Configuration (nginx/nginx.conf)
- Removed `/media/` location (files served directly from MinIO)

## Setup Instructions

### Option 1: Automated Setup (Recommended)
```bash
# Make the setup script executable
chmod +x setup_minio.sh

# Run the setup script
./setup_minio.sh
```

### Option 2: Manual Setup
```bash
# Stop existing containers
docker-compose down

# Build and start containers
docker-compose up -d --build

# Wait for MinIO to start (about 10 seconds)
sleep 10

# Setup MinIO bucket
docker-compose exec web python manage.py setup_minio

# Test file uploads
docker-compose exec web python manage.py test_file_upload
```

## Verification

### 1. Check MinIO Console
- Open http://localhost:9001 in your browser
- Login with:
  - Username: `minioadmin`
  - Password: `minioadmin123`
- You should see the `toytoi-media` bucket

### 2. Test File Upload
```bash
# Run the test command
docker-compose exec web python manage.py test_file_upload
```

### 3. Test through Django Admin
1. Go to Django admin
2. Create a new Game
3. Upload a cover image
4. Check that the image URL points to MinIO (should contain `:9000`)

## File URLs

Files uploaded through Django will now have URLs like:
```
http://localhost:9000/toytoi-media/game_covers/image.jpg?X-Amz-Algorithm=...
```

## Troubleshooting

### MinIO not accessible
- Check if MinIO container is running: `docker-compose ps minio`
- Check MinIO logs: `docker-compose logs minio`

### Bucket not found error
- Run: `docker-compose exec web python manage.py setup_minio`

### Files still saving locally
- Check Django settings are loaded correctly
- Verify environment variables: `docker-compose exec web python -c "from django.conf import settings; print(settings.STORAGES)"`

### Connection refused
- Make sure MinIO service is running
- Check that the endpoint uses `minio:9000` (Docker service name) not `localhost:9000`

## Production Considerations

For production deployment:

1. **Change credentials**: Update `MINIO_ROOT_USER` and `MINIO_ROOT_PASSWORD`
2. **Use HTTPS**: Set `AWS_S3_USE_SSL = True` in settings
3. **External MinIO**: Update `MINIO_ENDPOINT` to your MinIO server
4. **Backup**: Set up regular backups of MinIO data
5. **Security**: Configure proper access policies in MinIO

## File Storage Behavior

- **New uploads**: Stored in MinIO
- **Existing local files**: Remain in local storage until re-uploaded
- **Static files**: Can optionally be stored in MinIO (currently using local storage)

## Commands

- `python manage.py setup_minio` - Setup MinIO bucket and test connection
- `python manage.py test_file_upload` - Test file upload functionality
- `python test_minio.py` - Standalone MinIO connection test
