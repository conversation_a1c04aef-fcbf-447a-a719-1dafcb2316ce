#!/bin/bash

echo "🚀 Setting up MinIO for Django..."

# Stop existing containers
echo "Stopping existing containers..."
docker-compose down

# Build and start containers
echo "Building and starting containers..."
docker-compose up -d --build

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for Min<PERSON> to start..."
sleep 10

# Check if <PERSON><PERSON> is running
echo "Checking MinIO status..."
docker-compose ps minio

# Setup MinIO bucket
echo "Setting up MinIO bucket..."
docker-compose exec web python manage.py setup_minio

echo "✅ Setup complete!"
echo ""
echo "🌐 Access MinIO Console at: http://localhost:9001"
echo "   Username: minioadmin"
echo "   Password: minioadmin123"
echo ""
echo "🔧 MinIO API endpoint: http://localhost:9000"
echo "📦 Bucket name: toytoi-media"
echo ""
echo "To test file uploads, try uploading an image through Django admin or API."
