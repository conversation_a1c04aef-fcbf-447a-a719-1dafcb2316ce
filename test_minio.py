#!/usr/bin/env python
"""
Test script to verify MinIO connection and create bucket if needed.
Run this after starting your Docker containers.
"""
import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'DjangoBaseApp.settings')
django.setup()

def test_minio_connection():
    """Test MinIO connection and create bucket if needed."""
    try:
        from minio import Minio
        from minio.error import S3Error
        
        # Get MinIO settings
        endpoint = settings.MINIO_ENDPOINT.replace('http://', '').replace('https://', '')
        access_key = settings.MINIO_ACCESS_KEY
        secret_key = settings.MINIO_SECRET_KEY
        bucket_name = settings.MINIO_BUCKET_NAME
        
        print(f"Connecting to MinIO at: {endpoint}")
        print(f"Using bucket: {bucket_name}")
        
        # Create MinIO client
        client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=False  # Set to True if using HTTPS
        )
        
        # Check if bucket exists
        if not client.bucket_exists(bucket_name):
            print(f"Bucket '{bucket_name}' does not exist. Creating...")
            client.make_bucket(bucket_name)
            print(f"Bucket '{bucket_name}' created successfully!")
        else:
            print(f"Bucket '{bucket_name}' already exists.")
        
        # Test upload
        test_content = b"Hello MinIO from Django!"
        test_file = "test-file.txt"
        
        client.put_object(
            bucket_name,
            test_file,
            data=test_content,
            length=len(test_content),
            content_type="text/plain"
        )
        print(f"Test file '{test_file}' uploaded successfully!")
        
        # Test download
        response = client.get_object(bucket_name, test_file)
        downloaded_content = response.read()
        print(f"Test file downloaded. Content: {downloaded_content.decode()}")
        
        # Clean up test file
        client.remove_object(bucket_name, test_file)
        print("Test file removed.")
        
        print("✅ MinIO connection test successful!")
        return True
        
    except S3Error as e:
        print(f"❌ MinIO S3 Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_django_storage():
    """Test Django's default storage backend."""
    try:
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        
        print("\nTesting Django storage backend...")
        print(f"Storage backend: {default_storage.__class__}")
        
        # Test file upload through Django
        test_content = ContentFile(b"Hello from Django storage!")
        file_name = default_storage.save("test-django-file.txt", test_content)
        print(f"File saved as: {file_name}")
        
        # Test file exists
        if default_storage.exists(file_name):
            print("File exists in storage.")
            
            # Test file URL
            file_url = default_storage.url(file_name)
            print(f"File URL: {file_url}")
            
            # Clean up
            default_storage.delete(file_name)
            print("Test file deleted.")
            
            print("✅ Django storage test successful!")
            return True
        else:
            print("❌ File was not saved properly.")
            return False
            
    except Exception as e:
        print(f"❌ Django storage error: {e}")
        return False

if __name__ == "__main__":
    print("=== MinIO Connection Test ===")
    minio_success = test_minio_connection()
    
    print("\n=== Django Storage Test ===")
    django_success = test_django_storage()
    
    if minio_success and django_success:
        print("\n🎉 All tests passed! MinIO is properly configured.")
    else:
        print("\n⚠️  Some tests failed. Check the configuration.")
