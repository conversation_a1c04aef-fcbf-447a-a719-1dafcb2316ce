from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from toy_app.models import Game


class Command(BaseCommand):
    help = 'Test file upload to MinIO through Django'

    def handle(self, *args, **options):
        self.stdout.write("Testing file upload to MinIO...")
        
        try:
            # Test 1: Direct storage test
            self.stdout.write("Test 1: Direct storage upload...")
            test_content = ContentFile(b"Test image content for MinIO")
            file_name = default_storage.save("test-uploads/test-image.txt", test_content)
            
            self.stdout.write(f"File saved as: {file_name}")
            
            # Check if file exists
            if default_storage.exists(file_name):
                file_url = default_storage.url(file_name)
                self.stdout.write(f"File URL: {file_url}")
                self.stdout.write(self.style.SUCCESS("✅ Direct storage test passed!"))
                
                # Clean up
                default_storage.delete(file_name)
                self.stdout.write("Test file cleaned up.")
            else:
                self.stdout.write(self.style.ERROR("❌ File was not saved properly"))
                return
            
            # Test 2: Model field test
            self.stdout.write("\nTest 2: Model ImageField test...")
            
            # Create a test game with cover image
            test_image_content = ContentFile(b"Fake image content for testing")
            test_image_content.name = "test-game-cover.jpg"
            
            game = Game.objects.create(
                title="Test Game for MinIO",
                description="This is a test game to verify MinIO integration",
                price=9.99
            )
            
            # Save the image to the model field
            game.cover_image.save("test-cover.jpg", test_image_content, save=True)
            
            if game.cover_image:
                image_url = game.cover_image.url
                self.stdout.write(f"Game cover image URL: {image_url}")
                self.stdout.write(self.style.SUCCESS("✅ Model ImageField test passed!"))
                
                # Clean up
                game.cover_image.delete(save=False)
                game.delete()
                self.stdout.write("Test game cleaned up.")
            else:
                self.stdout.write(self.style.ERROR("❌ Model ImageField test failed"))
                game.delete()
                return
            
            self.stdout.write(self.style.SUCCESS("\n🎉 All file upload tests passed!"))
            self.stdout.write("Django is successfully using MinIO for file storage.")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed with error: {e}"))
