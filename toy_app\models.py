from datetime import timed<PERSON>ta

from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager
from django.db import models
from django.utils import timezone
from DjangoBaseApp import settings


# Create your models here.


# --- Пользователь ---
class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('Email должен быть указан')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(unique=True)
    phone = models.Char<PERSON>ield(max_length=11, unique=True, blank=True,null=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []  # Не нужно username при createsuperuser

    objects = UserManager()

    def __str__(self):
        return self.email


class EmailVerificationCode(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)

    def is_expired(self):
        return self.created_at + timedelta(minutes=10) < timezone.now()

    def __str__(self):
        return f"{self.user.email} – {self.code}"


# --- Игра ---
class Game(models.Model):
    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255, blank=True, null=True)  # Доп. подзаголовок
    description = models.TextField(help_text="Основное описание игры")

    how_to_play = models.TextField(blank=True, null=True, help_text="Как играть")
    target_audience = models.TextField(blank=True, null=True, help_text="Для кого игра")

    requires_device = models.BooleanField(default=False)

    price = models.DecimalField(max_digits=10, decimal_places=2)
    trial_available = models.BooleanField(default=True)

    cover_image = models.ImageField(upload_to='game_covers/', blank=True, null=True)
    gallery_images = models.JSONField(blank=True, null=True, help_text="Список картинок или видео")  # Для галереи

    # Новые поля:
    system_requirements = models.TextField(blank=True, null=True, help_text="Требования к системе")
    required_equipment = models.TextField(blank=True, null=True, help_text="Оборудование (микрофоны и т.д.)")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title



class Bundle(models.Model):
    name = models.CharField(max_length=255)
    games = models.ManyToManyField(Game, blank=True)
    keys_count = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return self.name

# --- Ключи игры ---
class GameKey(models.Model):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='keys')
    code = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    assigned_to_user = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    assigned_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.game.title} - {self.code}"


class GameSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    game_key = models.ForeignKey(GameKey, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.email} - {self.game_key}"

class UserLibrary(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='library')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='library_entries')
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'game')  # Один пользователь — одна игра максимум

    def __str__(self):
        return f"{self.user.email} — {self.game.title}"


# --- Покупка ---
class Purchase(models.Model):
    PURCHASE_TYPES = (
        ('trial', 'Пробный доступ'),
        ('game', 'Игра'),
        ('key', 'Ключ'),
        ('bundle', 'Пакет ключей'),
    )
    STATUS_CHOICES = (
        ('pending', 'Ожидает оплаты'),
        ('paid', 'Оплачено'),
        ('cancelled', 'Отменено'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='purchases')
    game = models.ForeignKey(Game, on_delete=models.CASCADE)
    game_key = models.ForeignKey(GameKey, on_delete=models.SET_NULL, null=True, blank=True)
    purchase_type = models.CharField(max_length=20, choices=PURCHASE_TYPES)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.game.title} ({self.purchase_type})"



class CartItem(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='cart_items')
    game = models.ForeignKey(Game, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'game')


# --- Платежи ---
class Payment(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Ожидание'),
        ('success', 'Успешно'),
        ('failed', 'Ошибка'),
    )

    PROVIDER_CHOICES = (
        ('kaspi', 'Kaspi'),
        ('stripe', 'Stripe'),
        ('other', 'Другое'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    provider = models.CharField(max_length=50, choices=PROVIDER_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.provider} - {self.status}"



